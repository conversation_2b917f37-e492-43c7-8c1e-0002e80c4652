# ruff: noqa

import os

from web3 import Web3
from web3.constants import MAX_INT
from web3.middleware import geth_poa_middleware

# Before running this script you will need the following:
# - Install the web3 Python package (pip install -U web3==5.28)
# - A Polygon wallet funded with some MATIC

rpc_url = "https://polygon-rpc.com/"
priv_key = os.environ["POLYGON_PRIVATE_KEY"]
pub_key = os.environ["POLYGON_PUBLIC_KEY"]

chain_id = 137

erc20_approve = """[{"constant": false,"inputs": [{"name": "_spender","type": "address" },{ "name": "_value", "type": "uint256" }],"name": "approve","outputs": [{ "name": "", "type": "bool" }],"payable": false,"stateMutability": "nonpayable","type": "function"}]"""
erc1155_set_approval = """[{"inputs": [{ "internalType": "address", "name": "operator", "type": "address" },{ "internalType": "bool", "name": "approved", "type": "bool" }],"name": "setApprovalForAll","outputs": [],"stateMutability": "nonpayable","type": "function"}]"""

# Polymarket USDC token contract
usdc_address = "******************************************"

# Polymarket CTF contract
ctf_address = "******************************************"

web3 = Web3(Web3.HTTPProvider(rpc_url))
web3.middleware_onion.inject(geth_poa_middleware, layer=0)

nonce = web3.eth.getTransactionCount(pub_key)

usdc = web3.eth.contract(address=usdc_address, abi=erc20_approve)
ctf = web3.eth.contract(address=ctf_address, abi=erc1155_set_approval)

# CTF Exchange
raw_usdc_approve_txn = usdc.functions.approve(
    "******************************************",
    int(MAX_INT, 0),
).buildTransaction({"chainId": chain_id, "from": pub_key, "nonce": nonce})
signed_usdc_approve_tx = web3.eth.account.sign_transaction(
    raw_usdc_approve_txn,
    private_key=priv_key,
)
send_usdc_approve_tx = web3.eth.send_raw_transaction(signed_usdc_approve_tx.rawTransaction)
usdc_approve_tx_receipt = web3.eth.wait_for_transaction_receipt(send_usdc_approve_tx, 600)
print(usdc_approve_tx_receipt)

nonce = web3.eth.getTransactionCount(pub_key)

raw_ctf_approval_txn = ctf.functions.setApprovalForAll(
    "******************************************",
    True,
).buildTransaction({"chainId": chain_id, "from": pub_key, "nonce": nonce})
signed_ctf_approval_tx = web3.eth.account.sign_transaction(
    raw_ctf_approval_txn,
    private_key=priv_key,
)
send_ctf_approval_tx = web3.eth.send_raw_transaction(signed_ctf_approval_tx.rawTransaction)
ctf_approval_tx_receipt = web3.eth.wait_for_transaction_receipt(send_ctf_approval_tx, 600)
print(ctf_approval_tx_receipt)

nonce = web3.eth.getTransactionCount(pub_key)

# Neg Risk CTF Exchange
raw_usdc_approve_txn = usdc.functions.approve(
    "******************************************",
    int(MAX_INT, 0),
).buildTransaction({"chainId": chain_id, "from": pub_key, "nonce": nonce})
signed_usdc_approve_tx = web3.eth.account.sign_transaction(
    raw_usdc_approve_txn,
    private_key=priv_key,
)
send_usdc_approve_tx = web3.eth.send_raw_transaction(signed_usdc_approve_tx.rawTransaction)
usdc_approve_tx_receipt = web3.eth.wait_for_transaction_receipt(send_usdc_approve_tx, 600)
print(usdc_approve_tx_receipt)

nonce = web3.eth.getTransactionCount(pub_key)

raw_ctf_approval_txn = ctf.functions.setApprovalForAll(
    "******************************************",
    True,
).buildTransaction({"chainId": chain_id, "from": pub_key, "nonce": nonce})
signed_ctf_approval_tx = web3.eth.account.sign_transaction(
    raw_ctf_approval_txn,
    private_key=priv_key,
)
send_ctf_approval_tx = web3.eth.send_raw_transaction(signed_ctf_approval_tx.rawTransaction)
ctf_approval_tx_receipt = web3.eth.wait_for_transaction_receipt(send_ctf_approval_tx, 600)
print(ctf_approval_tx_receipt)

nonce = web3.eth.getTransactionCount(pub_key)

# Neg Risk Adapter
raw_usdc_approve_txn = usdc.functions.approve(
    "******************************************",
    int(MAX_INT, 0),
).buildTransaction({"chainId": chain_id, "from": pub_key, "nonce": nonce})
signed_usdc_approve_tx = web3.eth.account.sign_transaction(
    raw_usdc_approve_txn,
    private_key=priv_key,
)
send_usdc_approve_tx = web3.eth.send_raw_transaction(signed_usdc_approve_tx.rawTransaction)
usdc_approve_tx_receipt = web3.eth.wait_for_transaction_receipt(send_usdc_approve_tx, 600)
print(usdc_approve_tx_receipt)

nonce = web3.eth.getTransactionCount(pub_key)

raw_ctf_approval_txn = ctf.functions.setApprovalForAll(
    "******************************************",
    True,
).buildTransaction({"chainId": chain_id, "from": pub_key, "nonce": nonce})
signed_ctf_approval_tx = web3.eth.account.sign_transaction(
    raw_ctf_approval_txn,
    private_key=priv_key,
)
send_ctf_approval_tx = web3.eth.send_raw_transaction(signed_ctf_approval_tx.rawTransaction)
ctf_approval_tx_receipt = web3.eth.wait_for_transaction_receipt(send_ctf_approval_tx, 600)
print(ctf_approval_tx_receipt)
