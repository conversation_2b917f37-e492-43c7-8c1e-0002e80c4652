/* Generated with cbindgen:0.29.0 */

/* Warning, this file is autogenerated by cbindgen. Don't modify this manually. */

#include <stdint.h>
#include <Python.h>

#define HIGH_PRECISION

#ifdef __SIZEOF_INT128__
    typedef __uint128_t uint128_t;
    typedef __int128_t int128_t;
#endif

#define DEPTH10_LEN 10

/**
 * The maximum length of ASCII characters for a `TradeId` string value (including null terminator).
 */
#define TRADE_ID_LEN 37

#if defined(HIGH_PRECISION)
/**
 * The maximum fixed-point precision.
 */
#define FIXED_PRECISION 16
#endif

#if !defined(HIGH_PRECISION)
/**
 * The maximum fixed-point precision.
 */
#define FIXED_PRECISION 9
#endif

#if defined(HIGH_PRECISION)
/**
 * The scalar value corresponding to the maximum precision (10^16).
 */
#define FIXED_SCALAR 10000000000000000.0
#endif

#if !defined(HIGH_PRECISION)
/**
 * The scalar value corresponding to the maximum precision (10^9).
 */
#define FIXED_SCALAR 1000000000.0
#endif

/**
 * The scalar representing the difference between high-precision and standard-precision modes.
 */
#define PRECISION_DIFF_SCALAR 10000000.0

#if defined(HIGH_PRECISION)
/**
 * The maximum valid money amount which can be represented.
 */
#define MONEY_MAX **************.0
#endif

#if !defined(HIGH_PRECISION)
#define MONEY_MAX **********.0
#endif

#if defined(HIGH_PRECISION)
/**
 * The minimum valid money amount which can be represented.
 */
#define MONEY_MIN -**************.0
#endif

#if !defined(HIGH_PRECISION)
#define MONEY_MIN -**********.0
#endif

#if defined(HIGH_PRECISION)
/**
 * The maximum valid price value which can be represented.
 */
#define PRICE_MAX **************.0
#endif

#if !defined(HIGH_PRECISION)
#define PRICE_MAX **********.0
#endif

#if defined(HIGH_PRECISION)
/**
 * The minimum valid price value which can be represented.
 */
#define PRICE_MIN -**************.0
#endif

#if !defined(HIGH_PRECISION)
#define PRICE_MIN -**********.0
#endif

#if defined(HIGH_PRECISION)
/**
 * The maximum valid quantity value which can be represented.
 */
#define QUANTITY_MAX **************.0
#endif

#if !defined(HIGH_PRECISION)
#define QUANTITY_MAX ***********.0
#endif

/**
 * The minimum valid quantity value which can be represented.
 */
#define QUANTITY_MIN 0.0

/**
 * An account type provided by a trading venue or broker.
 */
typedef enum AccountType {
    /**
     * An account with unleveraged cash assets only.
     */
    CASH = 1,
    /**
     * An account which facilitates trading on margin, using account assets as collateral.
     */
    MARGIN = 2,
    /**
     * An account specific to betting markets.
     */
    BETTING = 3,
} AccountType;

/**
 * An aggregation source for derived data.
 */
typedef enum AggregationSource {
    /**
     * The data is externally aggregated (outside the Nautilus system boundary).
     */
    EXTERNAL = 1,
    /**
     * The data is internally aggregated (inside the Nautilus system boundary).
     */
    INTERNAL = 2,
} AggregationSource;

/**
 * The side for the aggressing order of a trade in a market.
 */
typedef enum AggressorSide {
    /**
     * There was no specific aggressor for the trade.
     */
    NO_AGGRESSOR = 0,
    /**
     * The BUY order was the aggressor for the trade.
     */
    BUYER = 1,
    /**
     * The SELL order was the aggressor for the trade.
     */
    SELLER = 2,
} AggressorSide;

/**
 * A broad financial market asset class.
 */
typedef enum AssetClass {
    /**
     * Foreign exchange (FOREX) assets.
     */
    FX = 1,
    /**
     * Equity / stock assets.
     */
    EQUITY = 2,
    /**
     * Commodity assets.
     */
    COMMODITY = 3,
    /**
     * Debt based assets.
     */
    DEBT = 4,
    /**
     * Index based assets (baskets).
     */
    INDEX = 5,
    /**
     * Cryptocurrency or crypto token assets.
     */
    CRYPTOCURRENCY = 6,
    /**
     * Alternative assets.
     */
    ALTERNATIVE = 7,
} AssetClass;

/**
 * The type of order book action for an order book event.
 */
typedef enum BookAction {
    /**
     * An order is added to the book.
     */
    ADD = 1,
    /**
     * An existing order in the book is updated/modified.
     */
    UPDATE = 2,
    /**
     * An existing order in the book is deleted/canceled.
     */
    DELETE = 3,
    /**
     * The state of the order book is cleared.
     */
    CLEAR = 4,
} BookAction;

/**
 * The order book type, representing the type of levels granularity and delta updating heuristics.
 */
typedef enum BookType {
    /**
     * Top-of-book best bid/ask, one level per side.
     */
    L1_MBP = 1,
    /**
     * Market by price, one order per level (aggregated).
     */
    L2_MBP = 2,
    /**
     * Market by order, multiple orders per level (full granularity).
     */
    L3_MBO = 3,
} BookType;

/**
 * The order contigency type which specifies the behavior of linked orders.
 *
 * [FIX 5.0 SP2 : ContingencyType <1385> field](https://www.onixs.biz/fix-dictionary/5.0.sp2/tagnum_1385.html).
 */
typedef enum ContingencyType {
    /**
     * Not a contingent order.
     */
    NO_CONTINGENCY = 0,
    /**
     * One-Cancels-the-Other.
     */
    OCO = 1,
    /**
     * One-Triggers-the-Other.
     */
    OTO = 2,
    /**
     * One-Updates-the-Other (by proportional quantity).
     */
    OUO = 3,
} ContingencyType;

/**
 * The broad currency type.
 */
typedef enum CurrencyType {
    /**
     * A type of cryptocurrency or crypto token.
     */
    CRYPTO = 1,
    /**
     * A type of currency issued by governments which is not backed by a commodity.
     */
    FIAT = 2,
    /**
     * A type of currency that is based on the value of an underlying commodity.
     */
    COMMODITY_BACKED = 3,
} CurrencyType;

/**
 * The instrument class.
 */
typedef enum InstrumentClass {
    /**
     * A spot market instrument class. The current market price of an instrument that is bought or sold for immediate delivery and payment.
     */
    SPOT = 1,
    /**
     * A swap instrument class. A derivative contract through which two parties exchange the cash flows or liabilities from two different financial instruments.
     */
    SWAP = 2,
    /**
     * A futures contract instrument class. A legal agreement to buy or sell an asset at a predetermined price at a specified time in the future.
     */
    FUTURE = 3,
    /**
     * A futures spread instrument class. A strategy involving the use of futures contracts to take advantage of price differentials between different contract months, underlying assets, or marketplaces.
     */
    FUTURES_SPREAD = 4,
    /**
     * A forward derivative instrument class. A customized contract between two parties to buy or sell an asset at a specified price on a future date.
     */
    FORWARD = 5,
    /**
     * A contract-for-difference (CFD) instrument class. A contract between an investor and a CFD broker to exchange the difference in the value of a financial product between the time the contract opens and closes.
     */
    CFD = 6,
    /**
     * A bond instrument class. A type of debt investment where an investor loans money to an entity (typically corporate or governmental) which borrows the funds for a defined period of time at a variable or fixed interest rate.
     */
    BOND = 7,
    /**
     * An option contract instrument class. A type of derivative that gives the holder the right, but not the obligation, to buy or sell an underlying asset at a predetermined price before or at a certain future date.
     */
    OPTION = 8,
    /**
     * An option spread instrument class. A strategy involving the purchase and/or sale of multiple option contracts on the same underlying asset with different strike prices or expiration dates to hedge risk or speculate on price movements.
     */
    OPTION_SPREAD = 9,
    /**
     * A warrant instrument class. A derivative that gives the holder the right, but not the obligation, to buy or sell a security—most commonly an equity—at a certain price before expiration.
     */
    WARRANT = 10,
    /**
     * A sports betting instrument class. A financialized derivative that allows wagering on the outcome of sports events using structured contracts or prediction markets.
     */
    SPORTS_BETTING = 11,
    /**
     * A binary option instrument class. A type of derivative where the payoff is either a fixed monetary amount or nothing, depending on whether the price of an underlying asset is above or below a predetermined level at expiration.
     * A binary option instrument class. A type of derivative where the payoff is either a fixed monetary amount or nothing, based on a yes/no proposition about an underlying event.
     */
    BINARY_OPTION = 12,
} InstrumentClass;

/**
 * The type of event for an instrument close.
 */
typedef enum InstrumentCloseType {
    /**
     * When the market session ended.
     */
    END_OF_SESSION = 1,
    /**
     * When the instrument expiration was reached.
     */
    CONTRACT_EXPIRED = 2,
} InstrumentCloseType;

/**
 * The liqudity side for a trade.
 */
typedef enum LiquiditySide {
    /**
     * No liquidity side specified.
     */
    NO_LIQUIDITY_SIDE = 0,
    /**
     * The order passively provided liqudity to the market to complete the trade (made a market).
     */
    MAKER = 1,
    /**
     * The order aggressively took liqudity from the market to complete the trade.
     */
    TAKER = 2,
} LiquiditySide;

/**
 * The status of an individual market on a trading venue.
 */
typedef enum MarketStatus {
    /**
     * The instrument is trading.
     */
    OPEN = 1,
    /**
     * The instrument is in a pre-open period.
     */
    CLOSED = 2,
    /**
     * Trading in the instrument has been paused.
     */
    PAUSED = 3,
    /**
     * Trading in the instrument has been halted.
     * Trading in the instrument has been suspended.
     */
    SUSPENDED = 5,
    /**
     * Trading in the instrument is not available.
     */
    NOT_AVAILABLE = 6,
} MarketStatus;

/**
 * An action affecting the status of an individual market on a trading venue.
 */
typedef enum MarketStatusAction {
    /**
     * No change.
     */
    NONE = 0,
    /**
     * The instrument is in a pre-open period.
     */
    PRE_OPEN = 1,
    /**
     * The instrument is in a pre-cross period.
     */
    PRE_CROSS = 2,
    /**
     * The instrument is quoting but not trading.
     */
    QUOTING = 3,
    /**
     * The instrument is in a cross/auction.
     */
    CROSS = 4,
    /**
     * The instrument is being opened through a trading rotation.
     */
    ROTATION = 5,
    /**
     * A new price indication is available for the instrument.
     */
    NEW_PRICE_INDICATION = 6,
    /**
     * The instrument is trading.
     */
    TRADING = 7,
    /**
     * Trading in the instrument has been halted.
     */
    HALT = 8,
    /**
     * Trading in the instrument has been paused.
     */
    PAUSE = 9,
    /**
     * Trading in the instrument has been suspended.
     */
    SUSPEND = 10,
    /**
     * The instrument is in a pre-close period.
     */
    PRE_CLOSE = 11,
    /**
     * Trading in the instrument has closed.
     */
    CLOSE = 12,
    /**
     * The instrument is in a post-close period.
     */
    POST_CLOSE = 13,
    /**
     * A change in short-selling restrictions.
     */
    SHORT_SELL_RESTRICTION_CHANGE = 14,
    /**
     * The instrument is not available for trading, either trading has closed or been halted.
     */
    NOT_AVAILABLE_FOR_TRADING = 15,
} MarketStatusAction;

/**
 * The order management system (OMS) type for a trading venue or trading strategy.
 */
typedef enum OmsType {
    /**
     * There is no specific type of order management specified (will defer to the venue OMS).
     */
    UNSPECIFIED = 0,
    /**
     * The netting type where there is one position per instrument.
     */
    NETTING = 1,
    /**
     * The hedging type where there can be multiple positions per instrument.
     * This can be in LONG/SHORT directions, by position/ticket ID, or tracked virtually by
     * Nautilus.
     */
    HEDGING = 2,
} OmsType;

/**
 * The kind of option contract.
 */
typedef enum OptionKind {
    /**
     * A Call option gives the holder the right, but not the obligation, to buy an underlying asset at a specified strike price within a specified period of time.
     */
    CALL = 1,
    /**
     * A Put option gives the holder the right, but not the obligation, to sell an underlying asset at a specified strike price within a specified period of time.
     */
    PUT = 2,
} OptionKind;

/**
 * The order side for a specific order, or action related to orders.
 */
typedef enum OrderSide {
    /**
     * No order side is specified.
     */
    NO_ORDER_SIDE = 0,
    /**
     * The order is a BUY.
     */
    BUY = 1,
    /**
     * The order is a SELL.
     */
    SELL = 2,
} OrderSide;

/**
 * The status for a specific order.
 *
 * An order is considered _open_ for the following status:
 *  - `ACCEPTED`
 *  - `TRIGGERED`
 *  - `PENDING_UPDATE`
 *  - `PENDING_CANCEL`
 *  - `PARTIALLY_FILLED`
 *
 * An order is considered _in-flight_ for the following status:
 *  - `SUBMITTED`
 *  - `PENDING_UPDATE`
 *  - `PENDING_CANCEL`
 *
 * An order is considered _closed_ for the following status:
 *  - `DENIED`
 *  - `REJECTED`
 *  - `CANCELED`
 *  - `EXPIRED`
 *  - `FILLED`
 */
typedef enum OrderStatus {
    /**
     * The order is initialized (instantiated) within the Nautilus system.
     */
    INITIALIZED = 1,
    /**
     * The order was denied by the Nautilus system, either for being invalid, unprocessable or exceeding a risk limit.
     */
    DENIED = 2,
    /**
     * The order became emulated by the Nautilus system in the `OrderEmulator` component.
     */
    EMULATED = 3,
    /**
     * The order was released by the Nautilus system from the `OrderEmulator` component.
     */
    RELEASED = 4,
    /**
     * The order was submitted by the Nautilus system to the external service or trading venue (awaiting acknowledgement).
     */
    SUBMITTED = 5,
    /**
     * The order was acknowledged by the trading venue as being received and valid (may now be working).
     */
    ACCEPTED = 6,
    /**
     * The order was rejected by the trading venue.
     */
    REJECTED = 7,
    /**
     * The order was canceled (closed/done).
     */
    CANCELED = 8,
    /**
     * The order reached a GTD expiration (closed/done).
     */
    EXPIRED = 9,
    /**
     * The order STOP price was triggered on a trading venue.
     */
    TRIGGERED = 10,
    /**
     * The order is currently pending a request to modify on a trading venue.
     */
    PENDING_UPDATE = 11,
    /**
     * The order is currently pending a request to cancel on a trading venue.
     */
    PENDING_CANCEL = 12,
    /**
     * The order has been partially filled on a trading venue.
     */
    PARTIALLY_FILLED = 13,
    /**
     * The order has been completely filled on a trading venue (closed/done).
     */
    FILLED = 14,
} OrderStatus;

/**
 * The type of order.
 */
typedef enum OrderType {
    /**
     * A market order to buy or sell at the best available price in the current market.
     */
    MARKET = 1,
    /**
     * A limit order to buy or sell at a specific price or better.
     */
    LIMIT = 2,
    /**
     * A stop market order to buy or sell once the price reaches the specified stop/trigger price. When the stop price is reached, the order effectively becomes a market order.
     */
    STOP_MARKET = 3,
    /**
     * A stop limit order to buy or sell which combines the features of a stop order and a limit order. Once the stop/trigger price is reached, a stop-limit order effectively becomes a limit order.
     */
    STOP_LIMIT = 4,
    /**
     * A market-to-limit order is a market order that is to be executed as a limit order at the current best market price after reaching the market.
     */
    MARKET_TO_LIMIT = 5,
    /**
     * A market-if-touched order effectively becomes a market order when the specified trigger price is reached.
     */
    MARKET_IF_TOUCHED = 6,
    /**
     * A limit-if-touched order effectively becomes a limit order when the specified trigger price is reached.
     */
    LIMIT_IF_TOUCHED = 7,
    /**
     * A trailing stop market order sets the stop/trigger price at a fixed "trailing offset" amount from the market.
     */
    TRAILING_STOP_MARKET = 8,
    /**
     * A trailing stop limit order combines the features of a trailing stop order with those of a limit order.
     */
    TRAILING_STOP_LIMIT = 9,
} OrderType;

/**
 * The market side for a specific position, or action related to positions.
 */
typedef enum PositionSide {
    /**
     * No position side is specified (only valid in the context of a filter for actions involving positions).
     */
    NO_POSITION_SIDE = 0,
    /**
     * A neural/flat position, where no position is currently held in the market.
     */
    FLAT = 1,
    /**
     * A long position in the market, typically acquired through one or many BUY orders.
     */
    LONG = 2,
    /**
     * A short position in the market, typically acquired through one or many SELL orders.
     */
    SHORT = 3,
} PositionSide;

/**
 * The type of price for an instrument in a market.
 */
typedef enum PriceType {
    /**
     * The best quoted price at which buyers are willing to buy a quantity of an instrument.
     * Often considered the best bid in the order book.
     */
    BID = 1,
    /**
     * The best quoted price at which sellers are willing to sell a quantity of an instrument.
     * Often considered the best ask in the order book.
     */
    ASK = 2,
    /**
     * The arithmetic midpoint between the best bid and ask quotes.
     */
    MID = 3,
    /**
     * The price at which the last trade of an instrument was executed.
     */
    LAST = 4,
    /**
     * A reference price reflecting an instrument's fair value, often used for portfolio
     * calculations and risk management.
     */
    MARK = 5,
} PriceType;

/**
 * A record flag bit field, indicating event end and data information.
 */
typedef enum RecordFlag {
    /**
     * Last message in the book event or packet from the venue for a given `instrument_id`.
     */
    F_LAST = (1 << 7),
    /**
     * Top-of-book message, not an individual order.
     */
    F_TOB = (1 << 6),
    /**
     * Message sourced from a replay, such as a snapshot server.
     */
    F_SNAPSHOT = (1 << 5),
    /**
     * Aggregated price level message, not an individual order.
     */
    F_MBP = (1 << 4),
    /**
     * Reserved for future use.
     */
    RESERVED_2 = (1 << 3),
    /**
     * Reserved for future use.
     */
    RESERVED_1 = (1 << 2),
} RecordFlag;

/**
 * The 'Time in Force' instruction for an order.
 */
typedef enum TimeInForce {
    /**
     * Good Till Cancel (GTC) - Remains active until canceled.
     */
    GTC = 1,
    /**
     * Immediate or Cancel (IOC) - Executes immediately to the extent possible, with any unfilled portion canceled.
     */
    IOC = 2,
    /**
     * Fill or Kill (FOK) - Executes in its entirety immediately or is canceled if full execution is not possible.
     */
    FOK = 3,
    /**
     * Good Till Date (GTD) - Remains active until the specified expiration date or time is reached.
     */
    GTD = 4,
    /**
     * Day - Remains active until the close of the current trading session.
     */
    DAY = 5,
    /**
     * At the Opening (ATO) - Executes at the market opening or expires if not filled.
     */
    AT_THE_OPEN = 6,
    /**
     * At the Closing (ATC) - Executes at the market close or expires if not filled.
     */
    AT_THE_CLOSE = 7,
} TimeInForce;

/**
 * The trading state for a node.
 */
typedef enum TradingState {
    /**
     * Normal trading operations.
     */
    ACTIVE = 1,
    /**
     * Trading is completely halted, no new order commands will be emitted.
     */
    HALTED = 2,
    /**
     * Only order commands which would cancel order, or reduce position sizes are permitted.
     */
    REDUCING = 3,
} TradingState;

/**
 * The trailing offset type for an order type which specifies a trailing stop/trigger or limit price.
 */
typedef enum TrailingOffsetType {
    /**
     * No trailing offset type is specified (invalid for trailing type orders).
     */
    NO_TRAILING_OFFSET = 0,
    /**
     * The trailing offset is based on a market price.
     */
    PRICE = 1,
    /**
     * The trailing offset is based on a percentage represented in basis points, of a market price.
     */
    BASIS_POINTS = 2,
    /**
     * The trailing offset is based on the number of ticks from a market price.
     */
    TICKS = 3,
    /**
     * The trailing offset is based on a price tier set by a specific trading venue.
     */
    PRICE_TIER = 4,
} TrailingOffsetType;

/**
 * The trigger type for the stop/trigger price of an order.
 */
typedef enum TriggerType {
    /**
     * No trigger type is specified (invalid for orders with a trigger).
     */
    NO_TRIGGER = 0,
    /**
     * The default trigger type set by the trading venue.
     */
    DEFAULT = 1,
    /**
     * Based on the last traded price for the instrument.
     */
    LAST_PRICE = 2,
    /**
     * Based on the mark price for the instrument.
     */
    MARK_PRICE = 3,
    /**
     * Based on the index price for the instrument.
     */
    INDEX_PRICE = 4,
    /**
     * Based on the top-of-book quoted prices for the instrument.
     */
    BID_ASK = 5,
    /**
     * Based on a 'double match' of the last traded price for the instrument
     */
    DOUBLE_LAST = 6,
    /**
     * Based on a 'double match' of the bid/ask price for the instrument
     */
    DOUBLE_BID_ASK = 7,
    /**
     * Based on both the [`TriggerType::LastPrice`] and [`TriggerType::BidAsk`].
     */
    LAST_OR_BID_ASK = 8,
    /**
     * Based on the mid-point of the [`TriggerType::BidAsk`].
     */
    MID_POINT = 9,
} TriggerType;

/**
 * Represents a discrete price level in an order book.
 *
 * Orders are stored in an [`IndexMap`] which preserves FIFO (insertion) order.
 */
typedef struct BookLevel BookLevel;

/**
 * Provides a high-performance, versatile order book.
 *
 * Maintains buy (bid) and sell (ask) orders in price-time priority, supporting multiple
 * market data formats:
 * - L3 (MBO): Market By Order - tracks individual orders with unique IDs.
 * - L2 (MBP): Market By Price - aggregates orders at each price level.
 * - L1 (MBP): Top-of-Book - maintains only the best bid and ask prices.
 */
typedef struct OrderBook OrderBook;

/**
 * Represents a grouped batch of `OrderBookDelta` updates for an `OrderBook`.
 *
 * This type cannot be `repr(C)` due to the `deltas` vec.
 */
typedef struct OrderBookDeltas_t OrderBookDeltas_t;

/**
 * Represents a synthetic instrument with prices derived from component instruments using a
 * formula.
 *
 * The `id` for the synthetic will become `{symbol}.{SYNTH}`.
 */
typedef struct SyntheticInstrument SyntheticInstrument;

/**
 * Represents a valid ticker symbol ID for a tradable instrument.
 */
typedef struct Symbol_t {
    char* _0;
} Symbol_t;

/**
 * Represents a valid trading venue ID.
 */
typedef struct Venue_t {
    char* _0;
} Venue_t;

/**
 * Represents a valid instrument ID.
 *
 * The symbol and venue combination should uniquely identify the instrument.
 */
typedef struct InstrumentId_t {
    /**
     * The instruments ticker symbol.
     */
    struct Symbol_t symbol;
    /**
     * The instruments trading venue.
     */
    struct Venue_t venue;
} InstrumentId_t;

#if defined(HIGH_PRECISION)
typedef int128_t PriceRaw;
#endif

#if !defined(HIGH_PRECISION)
typedef int64_t PriceRaw;
#endif

/**
 * Represents a price in a market.
 *
 * The number of decimal places may vary. For certain asset classes, prices may
 * have negative values. For example, prices for options instruments can be
 * negative under certain conditions.
 *
 * Handles up to {FIXED_PRECISION} decimals of precision.
 *
 *  - `PRICE_MAX` = {PRICE_MAX}
 *  - `PRICE_MIN` = {PRICE_MIN}
 */
typedef struct Price_t {
    /**
     * Represents the raw fixed-point value, with `precision` defining the number of decimal places.
     */
    PriceRaw raw;
    /**
     * The number of decimal places, with a maximum of {FIXED_PRECISION}.
     */
    uint8_t precision;
} Price_t;

#if defined(HIGH_PRECISION)
typedef uint128_t QuantityRaw;
#endif

#if !defined(HIGH_PRECISION)
typedef uint64_t QuantityRaw;
#endif

/**
 * Represents a quantity with a non-negative value.
 *
 * Capable of storing either a whole number (no decimal places) of 'contracts'
 * or 'shares' (instruments denominated in whole units) or a decimal value
 * containing decimal places for instruments denominated in fractional units.
 *
 * Handles up to {FIXED_PRECISION} decimals of precision.
 *
 * - `QUANTITY_MAX` = {QUANTITY_MAX}
 * - `QUANTITY_MIN` = 0
 */
typedef struct Quantity_t {
    /**
     * Represents the raw fixed-point value, with `precision` defining the number of decimal places.
     */
    QuantityRaw raw;
    /**
     * The number of decimal places, with a maximum of {FIXED_PRECISION}.
     */
    uint8_t precision;
} Quantity_t;

/**
 * Represents an order in a book.
 */
typedef struct BookOrder_t {
    /**
     * The order side.
     */
    enum OrderSide side;
    /**
     * The order price.
     */
    struct Price_t price;
    /**
     * The order size.
     */
    struct Quantity_t size;
    /**
     * The order ID.
     */
    uint64_t order_id;
} BookOrder_t;

/**
 * Represents a single change/delta in an order book.
 */
typedef struct OrderBookDelta_t {
    /**
     * The instrument ID for the book.
     */
    struct InstrumentId_t instrument_id;
    /**
     * The order book delta action.
     */
    enum BookAction action;
    /**
     * The order to apply.
     */
    struct BookOrder_t order;
    /**
     * The record flags bit field indicating event end and data information.
     */
    uint8_t flags;
    /**
     * The message sequence number assigned at the venue.
     */
    uint64_t sequence;
    /**
     * UNIX timestamp (nanoseconds) when the book event occurred.
     */
    uint64_t ts_event;
    /**
     * UNIX timestamp (nanoseconds) when the struct was initialized.
     */
    uint64_t ts_init;
} OrderBookDelta_t;

/**
 * C compatible Foreign Function Interface (FFI) for an underlying [`OrderBookDeltas`].
 *
 * This struct wraps `OrderBookDeltas` in a way that makes it compatible with C function
 * calls, enabling interaction with `OrderBookDeltas` in a C environment.
 *
 * It implements the `Deref` trait, allowing instances of `OrderBookDeltas_API` to be
 * dereferenced to `OrderBookDeltas`, providing access to `OrderBookDeltas`'s methods without
 * having to manually access the underlying `OrderBookDeltas` instance.
 */
typedef struct OrderBookDeltas_API {
    struct OrderBookDeltas_t *_0;
} OrderBookDeltas_API;

/**
 * Represents a aggregated order book update with a fixed depth of 10 levels per side.
 *
 * This structure is specifically designed for scenarios where a snapshot of the top 10 bid and
 * ask levels in an order book is needed. It differs from `OrderBookDelta` or `OrderBookDeltas`
 * in its fixed-depth nature and is optimized for cases where a full depth representation is not
 * required or practical.
 *
 * Note: This type is not compatible with `OrderBookDelta` or `OrderBookDeltas` due to
 * its specialized structure and limited depth use case.
 */
typedef struct OrderBookDepth10_t {
    /**
     * The instrument ID for the book.
     */
    struct InstrumentId_t instrument_id;
    /**
     * The bid orders for the depth update.
     */
    struct BookOrder_t bids[DEPTH10_LEN];
    /**
     * The ask orders for the depth update.
     */
    struct BookOrder_t asks[DEPTH10_LEN];
    /**
     * The count of bid orders per level for the depth update.
     */
    uint32_t bid_counts[DEPTH10_LEN];
    /**
     * The count of ask orders per level for the depth update.
     */
    uint32_t ask_counts[DEPTH10_LEN];
    /**
     * The record flags bit field, indicating event end and data information.
     */
    uint8_t flags;
    /**
     * The message sequence number assigned at the venue.
     */
    uint64_t sequence;
    /**
     * UNIX timestamp (nanoseconds) when the book event occurred.
     */
    uint64_t ts_event;
    /**
     * UNIX timestamp (nanoseconds) when the struct was initialized.
     */
    uint64_t ts_init;
} OrderBookDepth10_t;

/**
 * Represents a quote tick in a market.
 */
typedef struct QuoteTick_t {
    /**
     * The quotes instrument ID.
     */
    struct InstrumentId_t instrument_id;
    /**
     * The top-of-book bid price.
     */
    struct Price_t bid_price;
    /**
     * The top-of-book ask price.
     */
    struct Price_t ask_price;
    /**
     * The top-of-book bid size.
     */
    struct Quantity_t bid_size;
    /**
     * The top-of-book ask size.
     */
    struct Quantity_t ask_size;
    /**
     * UNIX timestamp (nanoseconds) when the quote event occurred.
     */
    uint64_t ts_event;
    /**
     * UNIX timestamp (nanoseconds) when the struct was initialized.
     */
    uint64_t ts_init;
} QuoteTick_t;

/**
 * Represents a valid trade match ID (assigned by a trading venue).
 *
 * The unique ID assigned to the trade entity once it is received or matched by
 * the venue or central counterparty.
 *
 * Can correspond to the `TradeID <1003> field` of the FIX protocol.
 *
 * Maximum length is 36 characters.
 */
typedef struct TradeId_t {
    /**
     * The trade match ID value as a fixed-length C string byte array (includes null terminator).
     */
    uint8_t value[TRADE_ID_LEN];
} TradeId_t;

/**
 * Represents a trade tick in a market.
 */
typedef struct TradeTick_t {
    /**
     * The trade instrument ID.
     */
    struct InstrumentId_t instrument_id;
    /**
     * The traded price.
     */
    struct Price_t price;
    /**
     * The traded size.
     */
    struct Quantity_t size;
    /**
     * The trade aggressor side.
     */
    enum AggressorSide aggressor_side;
    /**
     * The trade match ID (assigned by the venue).
     */
    struct TradeId_t trade_id;
    /**
     * UNIX timestamp (nanoseconds) when the trade event occurred.
     */
    uint64_t ts_event;
    /**
     * UNIX timestamp (nanoseconds) when the struct was initialized.
     */
    uint64_t ts_init;
} TradeTick_t;

/**
 * Represents a bar aggregation specification including a step, aggregation
 * method/rule and price type.
 */
typedef struct BarSpecification_t {
    /**
     * The step for binning samples for bar aggregation.
     */
    uintptr_t step;
    /**
     * The type of bar aggregation.
     */
    uint8_t aggregation;
    /**
     * The price type to use for aggregation.
     */
    enum PriceType price_type;
} BarSpecification_t;

/**
 * Represents a bar type including the instrument ID, bar specification and
 * aggregation source.
 */
typedef enum BarType_t_Tag {
    STANDARD,
    COMPOSITE,
} BarType_t_Tag;

typedef struct Standard_Body {
    /**
     * The bar type's instrument ID.
     */
    struct InstrumentId_t instrument_id;
    /**
     * The bar type's specification.
     */
    struct BarSpecification_t spec;
    /**
     * The bar type's aggregation source.
     */
    enum AggregationSource aggregation_source;
} Standard_Body;

typedef struct Composite_Body {
    /**
     * The bar type's instrument ID.
     */
    struct InstrumentId_t instrument_id;
    /**
     * The bar type's specification.
     */
    struct BarSpecification_t spec;
    /**
     * The bar type's aggregation source.
     */
    enum AggregationSource aggregation_source;
    /**
     * The composite step for binning samples for bar aggregation.
     */
    uintptr_t composite_step;
    /**
     * The composite type of bar aggregation.
     */
    uint8_t composite_aggregation;
    /**
     * The composite bar type's aggregation source.
     */
    enum AggregationSource composite_aggregation_source;
} Composite_Body;

typedef struct BarType_t {
    BarType_t_Tag tag;
    union {
        Standard_Body STANDARD;
        Composite_Body COMPOSITE;
    };
} BarType_t;

/**
 * Represents an aggregated bar.
 */
typedef struct Bar_t {
    /**
     * The bar type for this bar.
     */
    struct BarType_t bar_type;
    /**
     * The bars open price.
     */
    struct Price_t open;
    /**
     * The bars high price.
     */
    struct Price_t high;
    /**
     * The bars low price.
     */
    struct Price_t low;
    /**
     * The bars close price.
     */
    struct Price_t close;
    /**
     * The bars volume.
     */
    struct Quantity_t volume;
    /**
     * UNIX timestamp (nanoseconds) when the data event occurred.
     */
    uint64_t ts_event;
    /**
     * UNIX timestamp (nanoseconds) when the struct was initialized.
     */
    uint64_t ts_init;
} Bar_t;

/**
 * Represents a mark price update.
 */
typedef struct MarkPriceUpdate_t {
    /**
     * The instrument ID for the mark price.
     */
    struct InstrumentId_t instrument_id;
    /**
     * The mark price.
     */
    struct Price_t value;
    /**
     * UNIX timestamp (nanoseconds) when the price event occurred.
     */
    uint64_t ts_event;
    /**
     * UNIX timestamp (nanoseconds) when the struct was initialized.
     */
    uint64_t ts_init;
} MarkPriceUpdate_t;

/**
 * Represents an index price update.
 */
typedef struct IndexPriceUpdate_t {
    /**
     * The instrument ID for the index price.
     */
    struct InstrumentId_t instrument_id;
    /**
     * The index price.
     */
    struct Price_t value;
    /**
     * UNIX timestamp (nanoseconds) when the price event occurred.
     */
    uint64_t ts_event;
    /**
     * UNIX timestamp (nanoseconds) when the struct was initialized.
     */
    uint64_t ts_init;
} IndexPriceUpdate_t;

/**
 * Represents an instrument close at a venue.
 */
typedef struct InstrumentClose_t {
    /**
     * The instrument ID.
     */
    struct InstrumentId_t instrument_id;
    /**
     * The closing price for the instrument.
     */
    struct Price_t close_price;
    /**
     * The type of closing price.
     */
    enum InstrumentCloseType close_type;
    /**
     * UNIX timestamp (nanoseconds) when the close price event occurred.
     */
    uint64_t ts_event;
    /**
     * UNIX timestamp (nanoseconds) when the struct was initialized.
     */
    uint64_t ts_init;
} InstrumentClose_t;

/**
 * A built-in Nautilus data type.
 *
 * Not recommended for storing large amounts of data, as the largest variant is significantly
 * larger (10x) than the smallest.
 */
typedef enum Data_t_Tag {
    DELTA,
    DELTAS,
    DEPTH10,
    QUOTE,
    TRADE,
    BAR,
    MARK_PRICE_UPDATE,
    INDEX_PRICE_UPDATE,
    INSTRUMENT_CLOSE,
} Data_t_Tag;

typedef struct Data_t {
    Data_t_Tag tag;
    union {
        struct {
            struct OrderBookDelta_t delta;
        };
        struct {
            struct OrderBookDeltas_API deltas;
        };
        struct {
            struct OrderBookDepth10_t *depth10;
        };
        struct {
            struct QuoteTick_t quote;
        };
        struct {
            struct TradeTick_t trade;
        };
        struct {
            struct Bar_t bar;
        };
        struct {
            struct MarkPriceUpdate_t mark_price_update;
        };
        struct {
            struct IndexPriceUpdate_t index_price_update;
        };
        struct {
            struct InstrumentClose_t instrument_close;
        };
    };
} Data_t;

/**
 * Represents a valid trader ID.
 */
typedef struct TraderId_t {
    char* _0;
} TraderId_t;

/**
 * Represents a valid strategy ID.
 */
typedef struct StrategyId_t {
    char* _0;
} StrategyId_t;

/**
 * Represents a valid client order ID (assigned by the Nautilus system).
 */
typedef struct ClientOrderId_t {
    char* _0;
} ClientOrderId_t;

/**
 * Represents an event where an order has been denied by the Nautilus system.
 *
 * This could be due an unsupported feature, a risk limit exceedance, or for
 * any other reason that an otherwise valid order is not able to be submitted.
 */
typedef struct OrderDenied_t {
    /**
     * The trader ID associated with the event.
     */
    struct TraderId_t trader_id;
    /**
     * The strategy ID associated with the event.
     */
    struct StrategyId_t strategy_id;
    /**
     * The instrument ID associated with the event.
     */
    struct InstrumentId_t instrument_id;
    /**
     * The client order ID associated with the event.
     */
    struct ClientOrderId_t client_order_id;
    /**
     * The reason the order was denied.
     */
    char* reason;
    /**
     * The unique identifier for the event.
     */
    UUID4_t event_id;
    /**
     * UNIX timestamp (nanoseconds) when the event occurred.
     */
    uint64_t ts_event;
    /**
     * UNIX timestamp (nanoseconds) when the event was initialized.
     */
    uint64_t ts_init;
} OrderDenied_t;

/**
 * Represents an event where an order has become emulated by the Nautilus system.
 */
typedef struct OrderEmulated_t {
    /**
     * The trader ID associated with the event.
     */
    struct TraderId_t trader_id;
    /**
     * The strategy ID associated with the event.
     */
    struct StrategyId_t strategy_id;
    /**
     * The instrument ID associated with the event.
     */
    struct InstrumentId_t instrument_id;
    /**
     * The client order ID associated with the event.
     */
    struct ClientOrderId_t client_order_id;
    /**
     * The unique identifier for the event.
     */
    UUID4_t event_id;
    /**
     * UNIX timestamp (nanoseconds) when the event occurred.
     */
    uint64_t ts_event;
    /**
     * UNIX timestamp (nanoseconds) when the event was initialized.
     */
    uint64_t ts_init;
} OrderEmulated_t;

/**
 * Represents an event where an order was released from the `OrderEmulated` by the Nautilus system.
 */
typedef struct OrderReleased_t {
    /**
     * The trader ID associated with the event.
     */
    struct TraderId_t trader_id;
    /**
     * The strategy ID associated with the event.
     */
    struct StrategyId_t strategy_id;
    /**
     * The instrument ID associated with the event.
     */
    struct InstrumentId_t instrument_id;
    /**
     * The client order ID associated with the event.
     */
    struct ClientOrderId_t client_order_id;
    struct Price_t released_price;
    /**
     * The unique identifier for the event.
     */
    UUID4_t event_id;
    /**
     * UNIX timestamp (nanoseconds) when the event occurred.
     */
    uint64_t ts_event;
    /**
     * UNIX timestamp (nanoseconds) when the event was initialized.
     */
    uint64_t ts_init;
} OrderReleased_t;

/**
 * Represents a valid account ID.
 */
typedef struct AccountId_t {
    char* _0;
} AccountId_t;

/**
 * Represents an event where an order has been submitted by the system to the
 * trading venue.
 */
typedef struct OrderSubmitted_t {
    /**
     * The trader ID associated with the event.
     */
    struct TraderId_t trader_id;
    /**
     * The strategy ID associated with the event.
     */
    struct StrategyId_t strategy_id;
    /**
     * The instrument ID associated with the event.
     */
    struct InstrumentId_t instrument_id;
    /**
     * The client order ID associated with the event.
     */
    struct ClientOrderId_t client_order_id;
    /**
     * The account ID associated with the event.
     */
    struct AccountId_t account_id;
    /**
     * The unique identifier for the event.
     */
    UUID4_t event_id;
    /**
     * UNIX timestamp (nanoseconds) when the event occurred.
     */
    uint64_t ts_event;
    /**
     * UNIX timestamp (nanoseconds) when the event was initialized.
     */
    uint64_t ts_init;
} OrderSubmitted_t;

/**
 * Represents a valid venue order ID (assigned by a trading venue).
 */
typedef struct VenueOrderId_t {
    char* _0;
} VenueOrderId_t;

/**
 * Represents an event where an order has been accepted by the trading venue.
 *
 * This event often corresponds to a `NEW` OrdStatus <39> field in FIX execution reports.
 */
typedef struct OrderAccepted_t {
    /**
     * The trader ID associated with the event.
     */
    struct TraderId_t trader_id;
    /**
     * The strategy ID associated with the event.
     */
    struct StrategyId_t strategy_id;
    /**
     * The instrument ID associated with the event.
     */
    struct InstrumentId_t instrument_id;
    /**
     * The client order ID associated with the event.
     */
    struct ClientOrderId_t client_order_id;
    /**
     * The venue order ID associated with the event.
     */
    struct VenueOrderId_t venue_order_id;
    /**
     * The account ID associated with the event.
     */
    struct AccountId_t account_id;
    /**
     * The unique identifier for the event.
     */
    UUID4_t event_id;
    /**
     * UNIX timestamp (nanoseconds) when the event occurred.
     */
    uint64_t ts_event;
    /**
     * UNIX timestamp (nanoseconds) when the event was initialized.
     */
    uint64_t ts_init;
    /**
     * If the event was generated during reconciliation.
     */
    uint8_t reconciliation;
} OrderAccepted_t;

/**
 * Represents an event where an order has been rejected by the trading venue.
 */
typedef struct OrderRejected_t {
    /**
     * The trader ID associated with the event.
     */
    struct TraderId_t trader_id;
    /**
     * The strategy ID associated with the event.
     */
    struct StrategyId_t strategy_id;
    /**
     * The instrument ID associated with the event.
     */
    struct InstrumentId_t instrument_id;
    /**
     * The client order ID associated with the event.
     */
    struct ClientOrderId_t client_order_id;
    /**
     * The account ID associated with the event.
     */
    struct AccountId_t account_id;
    /**
     * The reason the order was rejected.
     */
    char* reason;
    /**
     * The unique identifier for the event.
     */
    UUID4_t event_id;
    /**
     * UNIX timestamp (nanoseconds) when the event occurred.
     */
    uint64_t ts_event;
    /**
     * UNIX timestamp (nanoseconds) when the event was initialized.
     */
    uint64_t ts_init;
    /**
     * If the event was generated during reconciliation.
     */
    uint8_t reconciliation;
} OrderRejected_t;

/**
 * Represents a system client ID.
 */
typedef struct ClientId_t {
    char* _0;
} ClientId_t;

/**
 * Represents a valid component ID.
 */
typedef struct ComponentId_t {
    char* _0;
} ComponentId_t;

/**
 * Represents a valid execution algorithm ID.
 */
typedef struct ExecAlgorithmId_t {
    char* _0;
} ExecAlgorithmId_t;

/**
 * Represents a valid order list ID (assigned by the Nautilus system).
 */
typedef struct OrderListId_t {
    char* _0;
} OrderListId_t;

/**
 * Represents a valid position ID.
 */
typedef struct PositionId_t {
    char* _0;
} PositionId_t;

/**
 * C compatible Foreign Function Interface (FFI) for an underlying
 * [`SyntheticInstrument`].
 *
 * This struct wraps `SyntheticInstrument` in a way that makes it compatible with C function
 * calls, enabling interaction with `SyntheticInstrument` in a C environment.
 *
 * It implements the `Deref` trait, allowing instances of `SyntheticInstrument_API` to be
 * dereferenced to `SyntheticInstrument`, providing access to `SyntheticInstruments`'s methods without
 * having to manually access the underlying instance.
 */
typedef struct SyntheticInstrument_API {
    struct SyntheticInstrument *_0;
} SyntheticInstrument_API;

/**
 * C compatible Foreign Function Interface (FFI) for an underlying `OrderBook`.
 *
 * This struct wraps `OrderBook` in a way that makes it compatible with C function
 * calls, enabling interaction with `OrderBook` in a C environment.
 *
 * It implements the `Deref` trait, allowing instances of `OrderBook_API` to be
 * dereferenced to `OrderBook`, providing access to `OrderBook`'s methods without
 * having to manually access the underlying `OrderBook` instance.
 */
typedef struct OrderBook_API {
    struct OrderBook *_0;
} OrderBook_API;

/**
 * C compatible Foreign Function Interface (FFI) for an underlying order book[`BookLevel`].
 *
 * This struct wraps `Level` in a way that makes it compatible with C function
 * calls, enabling interaction with `Level` in a C environment.
 *
 * It implements the `Deref` trait, allowing instances of `Level_API` to be
 * dereferenced to `Level`, providing access to `Level`'s methods without
 * having to manually acce wss the underlying `Level` instance.
 */
typedef struct BookLevel_API {
    struct BookLevel *_0;
} BookLevel_API;

/**
 * Represents a medium of exchange in a specified denomination with a fixed decimal precision.
 *
 * Handles up to {FIXED_PRECISION} decimals of precision.
 */
typedef struct Currency_t {
    /**
     * The currency code as an alpha-3 string (e.g., "USD", "EUR").
     */
    char* code;
    /**
     * The currency decimal precision.
     */
    uint8_t precision;
    /**
     * The ISO 4217 currency code.
     */
    uint16_t iso4217;
    /**
     * The full name of the currency.
     */
    char* name;
    /**
     * The currency type, indicating its category (e.g. Fiat, Crypto).
     */
    enum CurrencyType currency_type;
} Currency_t;

#if defined(HIGH_PRECISION)
typedef int128_t MoneyRaw;
#endif

#if !defined(HIGH_PRECISION)
typedef int64_t MoneyRaw;
#endif

/**
 * Represents an amount of money in a specified currency denomination.
 *
 * - `MONEY_MAX` = {MONEY_MAX}
 * - `MONEY_MIN` = {MONEY_MIN}
 */
typedef struct Money_t {
    /**
     * Represents the raw fixed-point amount, with `currency.precision` defining the number of decimal places.
     */
    MoneyRaw raw;
    /**
     * The currency denomination associated with the monetary amount.
     */
    struct Currency_t currency;
} Money_t;

/**
 * Represents a NULL book order (used with the `Clear` action or where an order is not specified).
 */
#define NULL_ORDER (BookOrder_t){ .side = OrderSide_NoOrderSide, .price = (Price_t){ .raw = 0, .precision = 0 }, .size = (Quantity_t){ .raw = 0, .precision = 0 }, .order_id = 0 }





/**
 * The sentinel `Price` representing errors (this will be removed when Cython is gone).
 */
#define ERROR_PRICE (Price_t){ .raw = 0, .precision = 255 }



/**
 * Indicates if high_precision mode is enabled.
 */
extern const uint8_t HIGH_PRECISION_MODE;

#if defined(HIGH_PRECISION)
/**
 * The width in bytes for fixed-point value types in high-precision mode (128-bit).
 */
extern const int32_t PRECISION_BYTES;
#endif

#if !defined(HIGH_PRECISION)
/**
 * The width in bytes for fixed-point value types in standard-precision mode (64-bit).
 */
extern const int32_t PRECISION_BYTES;
#endif

/**
 * The maximum raw price integer value.
 */
extern const PriceRaw PRICE_RAW_MAX;

/**
 * The minimum raw price integer value.
 */
extern const PriceRaw PRICE_RAW_MIN;

/**
 * The maximum raw quantity integer value.
 */
extern const QuantityRaw QUANTITY_RAW_MAX;

/**
 * Clones a data instance.
 */
struct Data_t data_clone(const struct Data_t *data);

/**
 * # Panics
 *
 * Panics if `aggregation` or `price_type` do not correspond to valid enum variants.
 */
struct BarSpecification_t bar_specification_new(uintptr_t step,
                                                uint8_t aggregation,
                                                uint8_t price_type);

/**
 * Returns a [`BarSpecification`] as a C string pointer.
 */
const char *bar_specification_to_cstr(const struct BarSpecification_t *bar_spec);

uint64_t bar_specification_hash(const struct BarSpecification_t *bar_spec);

uint8_t bar_specification_eq(const struct BarSpecification_t *lhs,
                             const struct BarSpecification_t *rhs);

uint8_t bar_specification_lt(const struct BarSpecification_t *lhs,
                             const struct BarSpecification_t *rhs);

uint8_t bar_specification_le(const struct BarSpecification_t *lhs,
                             const struct BarSpecification_t *rhs);

uint8_t bar_specification_gt(const struct BarSpecification_t *lhs,
                             const struct BarSpecification_t *rhs);

uint8_t bar_specification_ge(const struct BarSpecification_t *lhs,
                             const struct BarSpecification_t *rhs);

/**
 * # Panics
 *
 * Panics if `aggregation_source` does not correspond to a valid enum variant.
 */
struct BarType_t bar_type_new(struct InstrumentId_t instrument_id,
                              struct BarSpecification_t spec,
                              uint8_t aggregation_source);

struct BarType_t bar_type_new_composite(struct InstrumentId_t instrument_id,
                                        struct BarSpecification_t spec,
                                        enum AggregationSource aggregation_source,
                                        uintptr_t composite_step,
                                        uint8_t composite_aggregation,
                                        enum AggregationSource composite_aggregation_source);

uint8_t bar_type_is_standard(const struct BarType_t *bar_type);

uint8_t bar_type_is_composite(const struct BarType_t *bar_type);

struct BarType_t bar_type_standard(const struct BarType_t *bar_type);

struct BarType_t bar_type_composite(const struct BarType_t *bar_type);

struct InstrumentId_t bar_type_instrument_id(const struct BarType_t *bar_type);

struct BarSpecification_t bar_type_spec(const struct BarType_t *bar_type);

enum AggregationSource bar_type_aggregation_source(const struct BarType_t *bar_type);

/**
 * Returns any [`BarType`] parsing error from the provided C string pointer.
 *
 * # Safety
 *
 * Assumes `ptr` is a valid C string pointer.
 */
const char *bar_type_check_parsing(const char *ptr);

/**
 * Returns a [`BarType`] from a C string pointer.
 *
 * # Safety
 *
 * Assumes `ptr` is a valid C string pointer.
 */
struct BarType_t bar_type_from_cstr(const char *ptr);

uint8_t bar_type_eq(const struct BarType_t *lhs, const struct BarType_t *rhs);

uint8_t bar_type_lt(const struct BarType_t *lhs, const struct BarType_t *rhs);

uint8_t bar_type_le(const struct BarType_t *lhs, const struct BarType_t *rhs);

uint8_t bar_type_gt(const struct BarType_t *lhs, const struct BarType_t *rhs);

uint8_t bar_type_ge(const struct BarType_t *lhs, const struct BarType_t *rhs);

uint64_t bar_type_hash(const struct BarType_t *bar_type);

/**
 * Returns a [`BarType`] as a C string pointer.
 */
const char *bar_type_to_cstr(const struct BarType_t *bar_type);

struct Bar_t bar_new(struct BarType_t bar_type,
                     struct Price_t open,
                     struct Price_t high,
                     struct Price_t low,
                     struct Price_t close,
                     struct Quantity_t volume,
                     uint64_t ts_event,
                     uint64_t ts_init);

uint8_t bar_eq(const struct Bar_t *lhs, const struct Bar_t *rhs);

uint64_t bar_hash(const struct Bar_t *bar);

/**
 * Returns a [`Bar`] as a C string.
 */
const char *bar_to_cstr(const struct Bar_t *bar);

struct OrderBookDelta_t orderbook_delta_new(struct InstrumentId_t instrument_id,
                                            enum BookAction action,
                                            struct BookOrder_t order,
                                            uint8_t flags,
                                            uint64_t sequence,
                                            uint64_t ts_event,
                                            uint64_t ts_init);

uint8_t orderbook_delta_eq(const struct OrderBookDelta_t *lhs, const struct OrderBookDelta_t *rhs);

uint64_t orderbook_delta_hash(const struct OrderBookDelta_t *delta);

/**
 * Creates a new [`OrderBookDeltas_API`] instance from a `CVec` of `OrderBookDelta`.
 *
 * # Safety
 *
 * - The `deltas` must be a valid pointer to a `CVec` containing `OrderBookDelta` objects.
 * - This function clones the data pointed to by `deltas` into Rust-managed memory, then forgets the original `Vec` to prevent Rust from auto-deallocating it.
 * - The caller is responsible for managing the memory of `deltas` (including its deallocation) to avoid memory leaks.
 */
struct OrderBookDeltas_API orderbook_deltas_new(struct InstrumentId_t instrument_id,
                                                const CVec *deltas);

void orderbook_deltas_drop(struct OrderBookDeltas_API deltas);

struct OrderBookDeltas_API orderbook_deltas_clone(const struct OrderBookDeltas_API *deltas);

struct InstrumentId_t orderbook_deltas_instrument_id(const struct OrderBookDeltas_API *deltas);

CVec orderbook_deltas_vec_deltas(const struct OrderBookDeltas_API *deltas);

uint8_t orderbook_deltas_is_snapshot(const struct OrderBookDeltas_API *deltas);

uint8_t orderbook_deltas_flags(const struct OrderBookDeltas_API *deltas);

uint64_t orderbook_deltas_sequence(const struct OrderBookDeltas_API *deltas);

uint64_t orderbook_deltas_ts_event(const struct OrderBookDeltas_API *deltas);

uint64_t orderbook_deltas_ts_init(const struct OrderBookDeltas_API *deltas);

void orderbook_deltas_vec_drop(CVec v);

/**
 * # Safety
 *
 * This function assumes:
 * - `bids` and `asks` are valid pointers to arrays of `BookOrder` of length 10.
 * - `bid_counts` and `ask_counts` are valid pointers to arrays of `u32` of length 10.
 *
 * # Panics
 *
 * Panics if any input pointer is null or if slice conversion for bids or asks fails.
 */
struct OrderBookDepth10_t orderbook_depth10_new(struct InstrumentId_t instrument_id,
                                                const struct BookOrder_t *bids_ptr,
                                                const struct BookOrder_t *asks_ptr,
                                                const uint32_t *bid_counts_ptr,
                                                const uint32_t *ask_counts_ptr,
                                                uint8_t flags,
                                                uint64_t sequence,
                                                uint64_t ts_event,
                                                uint64_t ts_init);

struct OrderBookDepth10_t orderbook_depth10_clone(const struct OrderBookDepth10_t *depth);

uint8_t orderbook_depth10_eq(const struct OrderBookDepth10_t *lhs,
                             const struct OrderBookDepth10_t *rhs);

uint64_t orderbook_depth10_hash(const struct OrderBookDepth10_t *delta);

const struct BookOrder_t *orderbook_depth10_bids_array(const struct OrderBookDepth10_t *depth);

const struct BookOrder_t *orderbook_depth10_asks_array(const struct OrderBookDepth10_t *depth);

const uint32_t *orderbook_depth10_bid_counts_array(const struct OrderBookDepth10_t *depth);

const uint32_t *orderbook_depth10_ask_counts_array(const struct OrderBookDepth10_t *depth);

struct BookOrder_t book_order_new(enum OrderSide order_side,
                                  struct Price_t price,
                                  struct Quantity_t size,
                                  uint64_t order_id);

uint8_t book_order_eq(const struct BookOrder_t *lhs, const struct BookOrder_t *rhs);

uint64_t book_order_hash(const struct BookOrder_t *order);

double book_order_exposure(const struct BookOrder_t *order);

double book_order_signed_size(const struct BookOrder_t *order);

/**
 * Returns a [`BookOrder`] display string as a C string pointer.
 */
const char *book_order_display_to_cstr(const struct BookOrder_t *order);

/**
 * Returns a [`BookOrder`] debug string as a C string pointer.
 */
const char *book_order_debug_to_cstr(const struct BookOrder_t *order);

struct MarkPriceUpdate_t mark_price_update_new(struct InstrumentId_t instrument_id,
                                               struct Price_t value,
                                               uint64_t ts_event,
                                               uint64_t ts_init);

uint8_t mark_price_update_eq(const struct MarkPriceUpdate_t *lhs,
                             const struct MarkPriceUpdate_t *rhs);

uint64_t mark_price_update_hash(const struct MarkPriceUpdate_t *value);

const char *mark_price_update_to_cstr(const struct MarkPriceUpdate_t *value);

struct QuoteTick_t quote_tick_new(struct InstrumentId_t instrument_id,
                                  struct Price_t bid_price,
                                  struct Price_t ask_price,
                                  struct Quantity_t bid_size,
                                  struct Quantity_t ask_size,
                                  uint64_t ts_event,
                                  uint64_t ts_init);

/**
 * # Panics
 *
 * Panics if any field of the two `QuoteTick` instances differs.
 */
uint8_t quote_tick_eq(const struct QuoteTick_t *lhs, const struct QuoteTick_t *rhs);

uint64_t quote_tick_hash(const struct QuoteTick_t *delta);

/**
 * Returns a [`QuoteTick`] as a C string pointer.
 */
const char *quote_tick_to_cstr(const struct QuoteTick_t *quote);

struct TradeTick_t trade_tick_new(struct InstrumentId_t instrument_id,
                                  struct Price_t price,
                                  struct Quantity_t size,
                                  enum AggressorSide aggressor_side,
                                  struct TradeId_t trade_id,
                                  uint64_t ts_event,
                                  uint64_t ts_init);

uint8_t trade_tick_eq(const struct TradeTick_t *lhs, const struct TradeTick_t *rhs);

uint64_t trade_tick_hash(const struct TradeTick_t *delta);

/**
 * Returns a [`TradeTick`] as a C string pointer.
 */
const char *trade_tick_to_cstr(const struct TradeTick_t *trade);

const char *account_type_to_cstr(enum AccountType value);

/**
 * Returns an enum from a Python string.
 *
 * # Safety
 *
 * Assumes `ptr` is a valid C string pointer.
 *
 * # Panics
 *
 * Panics if the C string does not correspond to a valid `AccountType` variant.
 */
enum AccountType account_type_from_cstr(const char *ptr);

const char *aggregation_source_to_cstr(enum AggregationSource value);

/**
 * Returns an enum from a Python string.
 *
 * # Safety
 *
 * Assumes `ptr` is a valid C string pointer.
 *
 * # Panics
 *
 * Panics if the C string does not correspond to a valid `AggressorSide` variant.
 */
enum AggregationSource aggregation_source_from_cstr(const char *ptr);

const char *aggressor_side_to_cstr(enum AggressorSide value);

/**
 * Returns an enum from a Python string.
 *
 * # Safety
 *
 * Assumes `ptr` is a valid C string pointer.
 *
 * # Panics
 *
 * Panics if the C string does not correspond to a valid `AggregationSource` variant.
 */
enum AggressorSide aggressor_side_from_cstr(const char *ptr);

const char *asset_class_to_cstr(enum AssetClass value);

/**
 * Returns an enum from a Python string.
 *
 * # Safety
 *
 * Assumes `ptr` is a valid C string pointer.
 *
 * # Panics
 *
 * Panics if the C string does not correspond to a valid `AssetClass` variant.
 */
enum AssetClass asset_class_from_cstr(const char *ptr);

const char *instrument_class_to_cstr(enum InstrumentClass value);

/**
 * Returns an enum from a Python string.
 *
 * # Safety
 *
 * Assumes `ptr` is a valid C string pointer.
 *
 * # Panics
 *
 * Panics if the C string does not correspond to a valid `InstrumentClass` variant.
 */
enum InstrumentClass instrument_class_from_cstr(const char *ptr);

const char *bar_aggregation_to_cstr(uint8_t value);

/**
 * Returns an enum from a Python string.
 *
 * # Safety
 *
 * Assumes `ptr` is a valid C string pointer.
 *
 * # Panics
 *
 * Panics if the C string does not correspond to a valid `BarAggregation` variant.
 */
uint8_t bar_aggregation_from_cstr(const char *ptr);

const char *book_action_to_cstr(enum BookAction value);

/**
 * Returns an enum from a Python string.
 *
 * # Safety
 *
 * Assumes `ptr` is a valid C string pointer.
 *
 * # Panics
 *
 * Panics if the C string does not correspond to a valid `BookAction` variant.
 */
enum BookAction book_action_from_cstr(const char *ptr);

const char *book_type_to_cstr(enum BookType value);

/**
 * Returns an enum from a Python string.
 *
 * # Safety
 *
 * Assumes `ptr` is a valid C string pointer.
 *
 * # Panics
 *
 * Panics if the C string does not correspond to a valid `BookType` variant.
 */
enum BookType book_type_from_cstr(const char *ptr);

const char *contingency_type_to_cstr(enum ContingencyType value);

/**
 * Returns an enum from a Python string.
 *
 * # Safety
 *
 * Assumes `ptr` is a valid C string pointer.
 *
 * # Panics
 *
 * Panics if the C string does not correspond to a valid `ContingencyType` variant.
 */
enum ContingencyType contingency_type_from_cstr(const char *ptr);

const char *currency_type_to_cstr(enum CurrencyType value);

/**
 * Returns an enum from a Python string.
 *
 * # Safety
 *
 * Assumes `ptr` is a valid C string pointer.
 *
 * # Panics
 *
 * Panics if the C string does not correspond to a valid `CurrencyType` variant.
 */
enum CurrencyType currency_type_from_cstr(const char *ptr);

/**
 * Returns an enum from a Python string.
 *
 * # Safety
 *
 * Assumes `ptr` is a valid C string pointer.
 *
 * # Panics
 *
 * Panics if the C string does not correspond to a valid `InstrumentCloseType` variant.
 */
enum InstrumentCloseType instrument_close_type_from_cstr(const char *ptr);

const char *instrument_close_type_to_cstr(enum InstrumentCloseType value);

const char *liquidity_side_to_cstr(enum LiquiditySide value);

/**
 * Returns an enum from a Python string.
 *
 * # Safety
 *
 * Assumes `ptr` is a valid C string pointer.
 *
 * # Panics
 *
 * Panics if the C string does not correspond to a valid `LiquiditySide` variant.
 */
enum LiquiditySide liquidity_side_from_cstr(const char *ptr);

const char *market_status_to_cstr(enum MarketStatus value);

/**
 * Returns an enum from a Python string.
 *
 * # Safety
 *
 * Assumes `ptr` is a valid C string pointer.
 *
 * # Panics
 *
 * Panics if the C string does not correspond to a valid `MarketStatus` variant.
 */
enum MarketStatus market_status_from_cstr(const char *ptr);

const char *market_status_action_to_cstr(enum MarketStatusAction value);

/**
 * Returns an enum from a Python string.
 *
 * # Safety
 *
 * Assumes `ptr` is a valid C string pointer.
 *
 * # Panics
 *
 * Panics if the C string does not correspond to a valid `MarketStatusAction` variant.
 */
enum MarketStatusAction market_status_action_from_cstr(const char *ptr);

const char *oms_type_to_cstr(enum OmsType value);

/**
 * Returns an enum from a Python string.
 *
 * # Safety
 *
 * Assumes `ptr` is a valid C string pointer.
 *
 * # Panics
 *
 * Panics if the C string does not correspond to a valid `OmsType` variant.
 */
enum OmsType oms_type_from_cstr(const char *ptr);

const char *option_kind_to_cstr(enum OptionKind value);

/**
 * Returns an enum from a Python string.
 *
 * # Safety
 *
 * Assumes `ptr` is a valid C string pointer.
 *
 * # Panics
 *
 * Panics if the C string does not correspond to a valid `OptionKind` variant.
 */
enum OptionKind option_kind_from_cstr(const char *ptr);

const char *order_side_to_cstr(enum OrderSide value);

/**
 * Returns an enum from a Python string.
 *
 * # Safety
 *
 * Assumes `ptr` is a valid C string pointer.
 *
 * # Panics
 *
 * Panics if the C string does not correspond to a valid `OrderSide` variant.
 */
enum OrderSide order_side_from_cstr(const char *ptr);

const char *order_status_to_cstr(enum OrderStatus value);

/**
 * Returns an enum from a Python string.
 *
 * # Safety
 *
 * Assumes `ptr` is a valid C string pointer.
 *
 * # Panics
 *
 * Panics if the C string does not correspond to a valid `OrderStatus` variant.
 */
enum OrderStatus order_status_from_cstr(const char *ptr);

const char *order_type_to_cstr(enum OrderType value);

/**
 * Returns an enum from a Python string.
 *
 * # Safety
 *
 * Assumes `ptr` is a valid C string pointer.
 *
 * # Panics
 *
 * Panics if the C string does not correspond to a valid `OrderType` variant.
 */
enum OrderType order_type_from_cstr(const char *ptr);

const char *position_side_to_cstr(enum PositionSide value);

/**
 * Returns an enum from a Python string.
 *
 * # Safety
 *
 * Assumes `ptr` is a valid C string pointer.
 *
 * # Panics
 *
 * Panics if the C string does not correspond to a valid `PositionSide` variant.
 */
enum PositionSide position_side_from_cstr(const char *ptr);

const char *price_type_to_cstr(enum PriceType value);

/**
 * Returns an enum from a Python string.
 *
 * # Safety
 *
 * Assumes `ptr` is a valid C string pointer.
 *
 * # Panics
 *
 * Panics if the C string does not correspond to a valid `PriceType` variant.
 */
enum PriceType price_type_from_cstr(const char *ptr);

const char *record_flag_to_cstr(enum RecordFlag value);

/**
 * Returns an enum from a Python string.
 *
 * # Safety
 *
 * Assumes `ptr` is a valid C string pointer.
 *
 * # Panics
 *
 * Panics if the C string does not correspond to a valid `RecordFlag` variant.
 */
enum RecordFlag record_flag_from_cstr(const char *ptr);

const char *time_in_force_to_cstr(enum TimeInForce value);

/**
 * Returns an enum from a Python string.
 *
 * # Safety
 *
 * Assumes `ptr` is a valid C string pointer.
 *
 * # Panics
 *
 * Panics if the C string does not correspond to a valid `TimeInForce` variant.
 */
enum TimeInForce time_in_force_from_cstr(const char *ptr);

const char *trading_state_to_cstr(enum TradingState value);

/**
 * Returns an enum from a Python string.
 *
 * # Safety
 *
 * Assumes `ptr` is a valid C string pointer.
 *
 * # Panics
 *
 * Panics if the C string does not correspond to a valid `TradingState` variant.
 */
enum TradingState trading_state_from_cstr(const char *ptr);

const char *trailing_offset_type_to_cstr(enum TrailingOffsetType value);

/**
 * Returns an enum from a Python string.
 *
 * # Safety
 *
 * Assumes `ptr` is a valid C string pointer.
 *
 * # Panics
 *
 * Panics if the C string does not correspond to a valid `TrailingOffsetType` variant.
 */
enum TrailingOffsetType trailing_offset_type_from_cstr(const char *ptr);

const char *trigger_type_to_cstr(enum TriggerType value);

/**
 * Returns an enum from a Python string.
 *
 * # Safety
 *
 * Assumes `ptr` is a valid C string pointer.
 *
 * # Panics
 *
 * Panics if the C string does not correspond to a valid `TriggerType` variant.
 */
enum TriggerType trigger_type_from_cstr(const char *ptr);

/**
 * # Safety
 *
 * Assumes `reason_ptr` is a valid C string pointer.
 */
struct OrderDenied_t order_denied_new(struct TraderId_t trader_id,
                                      struct StrategyId_t strategy_id,
                                      struct InstrumentId_t instrument_id,
                                      struct ClientOrderId_t client_order_id,
                                      const char *reason_ptr,
                                      UUID4_t event_id,
                                      uint64_t ts_event,
                                      uint64_t ts_init);

struct OrderEmulated_t order_emulated_new(struct TraderId_t trader_id,
                                          struct StrategyId_t strategy_id,
                                          struct InstrumentId_t instrument_id,
                                          struct ClientOrderId_t client_order_id,
                                          UUID4_t event_id,
                                          uint64_t ts_event,
                                          uint64_t ts_init);

struct OrderReleased_t order_released_new(struct TraderId_t trader_id,
                                          struct StrategyId_t strategy_id,
                                          struct InstrumentId_t instrument_id,
                                          struct ClientOrderId_t client_order_id,
                                          struct Price_t released_price,
                                          UUID4_t event_id,
                                          uint64_t ts_event,
                                          uint64_t ts_init);

struct OrderSubmitted_t order_submitted_new(struct TraderId_t trader_id,
                                            struct StrategyId_t strategy_id,
                                            struct InstrumentId_t instrument_id,
                                            struct ClientOrderId_t client_order_id,
                                            struct AccountId_t account_id,
                                            UUID4_t event_id,
                                            uint64_t ts_event,
                                            uint64_t ts_init);

struct OrderAccepted_t order_accepted_new(struct TraderId_t trader_id,
                                          struct StrategyId_t strategy_id,
                                          struct InstrumentId_t instrument_id,
                                          struct ClientOrderId_t client_order_id,
                                          struct VenueOrderId_t venue_order_id,
                                          struct AccountId_t account_id,
                                          UUID4_t event_id,
                                          uint64_t ts_event,
                                          uint64_t ts_init,
                                          uint8_t reconciliation);

/**
 * # Safety
 *
 * Assumes `reason_ptr` is a valid C string pointer.
 */
struct OrderRejected_t order_rejected_new(struct TraderId_t trader_id,
                                          struct StrategyId_t strategy_id,
                                          struct InstrumentId_t instrument_id,
                                          struct ClientOrderId_t client_order_id,
                                          struct AccountId_t account_id,
                                          const char *reason_ptr,
                                          UUID4_t event_id,
                                          uint64_t ts_event,
                                          uint64_t ts_init,
                                          uint8_t reconciliation);

/**
 * FFI wrapper for interned string statistics.
 */
void interned_string_stats(void);

/**
 * Returns a Nautilus identifier from a C string pointer.
 *
 * # Safety
 *
 * Assumes `ptr` is a valid C string pointer.
 */
struct AccountId_t account_id_new(const char *ptr);

uint64_t account_id_hash(const struct AccountId_t *id);

/**
 * Returns a Nautilus identifier from C string pointer.
 *
 * # Safety
 *
 * Assumes `ptr` is a valid C string pointer.
 */
struct ClientId_t client_id_new(const char *ptr);

uint64_t client_id_hash(const struct ClientId_t *id);

/**
 * Returns a Nautilus identifier from a C string pointer.
 *
 * # Safety
 *
 * Assumes `ptr` is a valid C string pointer.
 */
struct ClientOrderId_t client_order_id_new(const char *ptr);

uint64_t client_order_id_hash(const struct ClientOrderId_t *id);

/**
 * Returns a Nautilus identifier from a C string pointer.
 *
 * # Safety
 *
 * Assumes `ptr` is a valid C string pointer.
 */
struct ComponentId_t component_id_new(const char *ptr);

uint64_t component_id_hash(const struct ComponentId_t *id);

/**
 * Returns a Nautilus identifier from a C string pointer.
 *
 * # Safety
 *
 * Assumes `ptr` is a valid C string pointer.
 */
struct ExecAlgorithmId_t exec_algorithm_id_new(const char *ptr);

uint64_t exec_algorithm_id_hash(const struct ExecAlgorithmId_t *id);

struct InstrumentId_t instrument_id_new(struct Symbol_t symbol, struct Venue_t venue);

/**
 * Returns any [`InstrumentId`] parsing error from the provided C string pointer.
 *
 * # Safety
 *
 * Assumes `ptr` is a valid C string pointer.
 */
const char *instrument_id_check_parsing(const char *ptr);

/**
 * Returns a Nautilus identifier from a C string pointer.
 *
 * # Safety
 *
 * Assumes `ptr` is a valid C string pointer.
 */
struct InstrumentId_t instrument_id_from_cstr(const char *ptr);

/**
 * Returns an [`InstrumentId`] as a C string pointer.
 */
const char *instrument_id_to_cstr(const struct InstrumentId_t *instrument_id);

uint64_t instrument_id_hash(const struct InstrumentId_t *instrument_id);

uint8_t instrument_id_is_synthetic(const struct InstrumentId_t *instrument_id);

/**
 * Returns a Nautilus identifier from a C string pointer.
 *
 * # Safety
 *
 * Assumes `ptr` is a valid C string pointer.
 */
struct OrderListId_t order_list_id_new(const char *ptr);

uint64_t order_list_id_hash(const struct OrderListId_t *id);

/**
 * Returns a Nautilus identifier from a C string pointer.
 *
 * # Safety
 *
 * Assumes `ptr` is a valid C string pointer.
 */
struct PositionId_t position_id_new(const char *ptr);

uint64_t position_id_hash(const struct PositionId_t *id);

/**
 * Returns a Nautilus identifier from a C string pointer.
 *
 * # Safety
 *
 * Assumes `ptr` is a valid C string pointer.
 */
struct StrategyId_t strategy_id_new(const char *ptr);

uint64_t strategy_id_hash(const struct StrategyId_t *id);

/**
 * Returns a Nautilus identifier from a C string pointer.
 *
 * # Safety
 *
 * Assumes `ptr` is a valid C string pointer.
 */
struct Symbol_t symbol_new(const char *ptr);

uint64_t symbol_hash(const struct Symbol_t *id);

uint8_t symbol_is_composite(const struct Symbol_t *id);

const char *symbol_root(const struct Symbol_t *id);

const char *symbol_topic(const struct Symbol_t *id);

/**
 * Returns a Nautilus identifier from a C string pointer.
 *
 * # Safety
 *
 * Assumes `ptr` is a valid C string pointer.
 */
struct TradeId_t trade_id_new(const char *ptr);

uint64_t trade_id_hash(const struct TradeId_t *id);

const char *trade_id_to_cstr(const struct TradeId_t *trade_id);

/**
 * Returns a Nautilus identifier from a C string pointer.
 *
 * # Safety
 *
 * Assumes `ptr` is a valid C string pointer.
 */
struct TraderId_t trader_id_new(const char *ptr);

uint64_t trader_id_hash(const struct TraderId_t *id);

/**
 * Returns a Nautilus identifier from a C string pointer.
 *
 * # Safety
 *
 * Assumes `ptr` is a valid C string pointer.
 */
struct Venue_t venue_new(const char *ptr);

uint64_t venue_hash(const struct Venue_t *id);

uint8_t venue_is_synthetic(const struct Venue_t *venue);

/**
 * Checks if a venue code exists in the internal map.
 *
 * # Safety
 *
 * Assumes `code_ptr` is a valid NUL-terminated UTF-8 C string pointer.
 *
 * # Panics
 *
 * Panics if the internal mutex `VENUE_MAP` is poisoned.
 */
uint8_t venue_code_exists(const char *code_ptr);

/**
 * Converts a UTF-8 C string pointer to a `Venue`.
 *
 * # Safety
 *
 * Assumes `code_ptr` is a valid NUL-terminated UTF-8 C string pointer.
 *
 * # Panics
 *
 * Panics if the code is not found or invalid (unwrap on `from_code`).
 */
struct Venue_t venue_from_cstr_code(const char *code_ptr);

/**
 * Returns a Nautilus identifier from a C string pointer.
 *
 * # Safety
 *
 * Assumes `ptr` is a valid C string pointer.
 */
struct VenueOrderId_t venue_order_id_new(const char *ptr);

uint64_t venue_order_id_hash(const struct VenueOrderId_t *id);

/**
 * Changes the formula of the synthetic instrument.
 *
 * # Panics
 *
 * Panics if the formula update operation fails (`unwrap`).
 *
 * # Safety
 *
 * This function assumes:
 * - `components_ptr` is a valid C string pointer of a JSON format list of strings.
 * - `formula_ptr` is a valid C string pointer.
 */
struct SyntheticInstrument_API synthetic_instrument_new(struct Symbol_t symbol,
                                                        uint8_t price_precision,
                                                        const char *components_ptr,
                                                        const char *formula_ptr,
                                                        uint64_t ts_event,
                                                        uint64_t ts_init);

void synthetic_instrument_drop(struct SyntheticInstrument_API synth);

struct InstrumentId_t synthetic_instrument_id(const struct SyntheticInstrument_API *synth);

uint8_t synthetic_instrument_price_precision(const struct SyntheticInstrument_API *synth);

struct Price_t synthetic_instrument_price_increment(const struct SyntheticInstrument_API *synth);

const char *synthetic_instrument_formula_to_cstr(const struct SyntheticInstrument_API *synth);

const char *synthetic_instrument_components_to_cstr(const struct SyntheticInstrument_API *synth);

uintptr_t synthetic_instrument_components_count(const struct SyntheticInstrument_API *synth);

uint64_t synthetic_instrument_ts_event(const struct SyntheticInstrument_API *synth);

uint64_t synthetic_instrument_ts_init(const struct SyntheticInstrument_API *synth);

/**
 * # Safety
 *
 * Assumes `formula_ptr` is a valid C string pointer.
 */
uint8_t synthetic_instrument_is_valid_formula(const struct SyntheticInstrument_API *synth,
                                              const char *formula_ptr);

/**
 * # Safety
 *
 * Assumes `formula_ptr` is a valid C string pointer.
 *
 * # Panics
 *
 * Panics if changing the formula fails (i.e., `unwrap()` in `change_formula`).
 */
void synthetic_instrument_change_formula(struct SyntheticInstrument_API *synth,
                                         const char *formula_ptr);

struct Price_t synthetic_instrument_calculate(struct SyntheticInstrument_API *synth,
                                              const CVec *inputs_ptr);

struct OrderBook_API orderbook_new(struct InstrumentId_t instrument_id, enum BookType book_type);

void orderbook_drop(struct OrderBook_API book);

void orderbook_reset(struct OrderBook_API *book);

struct InstrumentId_t orderbook_instrument_id(const struct OrderBook_API *book);

enum BookType orderbook_book_type(const struct OrderBook_API *book);

uint64_t orderbook_sequence(const struct OrderBook_API *book);

uint64_t orderbook_ts_last(const struct OrderBook_API *book);

uint64_t orderbook_update_count(const struct OrderBook_API *book);

void orderbook_add(struct OrderBook_API *book,
                   struct BookOrder_t order,
                   uint8_t flags,
                   uint64_t sequence,
                   uint64_t ts_event);

void orderbook_update(struct OrderBook_API *book,
                      struct BookOrder_t order,
                      uint8_t flags,
                      uint64_t sequence,
                      uint64_t ts_event);

void orderbook_delete(struct OrderBook_API *book,
                      struct BookOrder_t order,
                      uint8_t flags,
                      uint64_t sequence,
                      uint64_t ts_event);

void orderbook_clear(struct OrderBook_API *book, uint64_t sequence, uint64_t ts_event);

void orderbook_clear_bids(struct OrderBook_API *book, uint64_t sequence, uint64_t ts_event);

void orderbook_clear_asks(struct OrderBook_API *book, uint64_t sequence, uint64_t ts_event);

void orderbook_apply_delta(struct OrderBook_API *book, const struct OrderBookDelta_t *delta);

void orderbook_apply_deltas(struct OrderBook_API *book, const struct OrderBookDeltas_API *deltas);

void orderbook_apply_depth(struct OrderBook_API *book, const struct OrderBookDepth10_t *depth);

CVec orderbook_bids(struct OrderBook_API *book);

CVec orderbook_asks(struct OrderBook_API *book);

uint8_t orderbook_has_bid(struct OrderBook_API *book);

uint8_t orderbook_has_ask(struct OrderBook_API *book);

/**
 * # Panics
 *
 * Panics if there are no bid orders for best bid price.
 */
struct Price_t orderbook_best_bid_price(struct OrderBook_API *book);

/**
 * # Panics
 *
 * Panics if there are no ask orders for best ask price.
 */
struct Price_t orderbook_best_ask_price(struct OrderBook_API *book);

/**
 * # Panics
 *
 * Panics if there are no bid orders for best bid size.
 */
struct Quantity_t orderbook_best_bid_size(struct OrderBook_API *book);

/**
 * # Panics
 *
 * Panics if there are no ask orders for best ask size.
 */
struct Quantity_t orderbook_best_ask_size(struct OrderBook_API *book);

/**
 * # Panics
 *
 * Panics if unable to calculate spread (requires at least one bid and one ask).
 */
double orderbook_spread(struct OrderBook_API *book);

/**
 * # Panics
 *
 * Panics if unable to calculate midpoint (requires at least one bid and one ask).
 */
double orderbook_midpoint(struct OrderBook_API *book);

double orderbook_get_avg_px_for_quantity(struct OrderBook_API *book,
                                         struct Quantity_t qty,
                                         enum OrderSide order_side);

double orderbook_get_quantity_for_price(struct OrderBook_API *book,
                                        struct Price_t price,
                                        enum OrderSide order_side);

/**
 * Updates the order book with a quote tick.
 *
 * # Panics
 *
 * Panics if book type is not `L1_MBP`.
 */
void orderbook_update_quote_tick(struct OrderBook_API *book, const struct QuoteTick_t *quote);

/**
 * Updates the order book with a trade tick.
 *
 * # Panics
 *
 * Panics if book type is not `L1_MBP`.
 */
void orderbook_update_trade_tick(struct OrderBook_API *book, const struct TradeTick_t *trade);

CVec orderbook_simulate_fills(const struct OrderBook_API *book, struct BookOrder_t order);

uint8_t orderbook_check_integrity(const struct OrderBook_API *book);

void vec_fills_drop(CVec v);

/**
 * Returns a pretty printed `OrderBook` number of levels per side, as a C string pointer.
 */
const char *orderbook_pprint_to_cstr(const struct OrderBook_API *book, uintptr_t num_levels);

struct BookLevel_API level_new(enum OrderSide order_side, struct Price_t price, CVec orders);

void level_drop(struct BookLevel_API level);

struct BookLevel_API level_clone(const struct BookLevel_API *level);

enum OrderSide level_side(const struct BookLevel_API *level);

struct Price_t level_price(const struct BookLevel_API *level);

CVec level_orders(const struct BookLevel_API *level);

double level_size(const struct BookLevel_API *level);

double level_exposure(const struct BookLevel_API *level);

void vec_levels_drop(CVec v);

void vec_orders_drop(CVec v);

/**
 * Returns a [`Currency`] from pointers and primitives.
 *
 * # Safety
 *
 * This function assumes:
 * - `code_ptr` is a valid C string pointer.
 * - `name_ptr` is a valid C string pointer.
 */
struct Currency_t currency_from_py(const char *code_ptr,
                                   uint8_t precision,
                                   uint16_t iso4217,
                                   const char *name_ptr,
                                   enum CurrencyType currency_type);

const char *currency_to_cstr(const struct Currency_t *currency);

const char *currency_code_to_cstr(const struct Currency_t *currency);

const char *currency_name_to_cstr(const struct Currency_t *currency);

uint64_t currency_hash(const struct Currency_t *currency);

/**
 * Registers a currency in the global map for FFI.
 *
 * # Panics
 *
 * Panics if the internal mutex `CURRENCY_MAP` is poisoned when locking.
 */
void currency_register(struct Currency_t currency);

/**
 * Checks whether a currency code exists in the global map for FFI.
 *
 * # Panics
 *
 * Panics if the internal mutex `CURRENCY_MAP` is poisoned when locking.
 *
 * # Safety
 *
 * Assumes `code_ptr` is a valid NUL-terminated UTF-8 C string pointer.
 */
uint8_t currency_exists(const char *code_ptr);

/**
 * Converts a C string pointer to a `Currency` for FFI.
 *
 * # Panics
 *
 * Panics if the provided code string is invalid or not found (`unwrap`).
 *
 * # Safety
 *
 * Assumes `code_ptr` is a valid NUL-terminated UTF-8 C string pointer.
 */
struct Currency_t currency_from_cstr(const char *code_ptr);

struct Money_t money_new(double amount, struct Currency_t currency);

struct Money_t money_from_raw(MoneyRaw raw, struct Currency_t currency);

double money_as_f64(const struct Money_t *money);

void money_add_assign(struct Money_t a, struct Money_t b);

void money_sub_assign(struct Money_t a, struct Money_t b);

struct Price_t price_new(double value, uint8_t precision);

struct Price_t price_from_raw(PriceRaw raw, uint8_t precision);

double price_as_f64(const struct Price_t *price);

void price_add_assign(struct Price_t a, struct Price_t b);

void price_sub_assign(struct Price_t a, struct Price_t b);

struct Quantity_t quantity_new(double value, uint8_t precision);

struct Quantity_t quantity_from_raw(QuantityRaw raw, uint8_t precision);

double quantity_as_f64(const struct Quantity_t *qty);

void quantity_add_assign(struct Quantity_t a, struct Quantity_t b);

void quantity_add_assign_u64(struct Quantity_t a, uint64_t b);

void quantity_sub_assign(struct Quantity_t a, struct Quantity_t b);

void quantity_sub_assign_u64(struct Quantity_t a, uint64_t b);
