# Warning, this file is autogenerated by cbindgen. Don't modify this manually. */

from cpython.object cimport PyObject
from libc.stdint cimport uint8_t, uint64_t, uintptr_t
from nautilus_trader.core.rust.common cimport TestClock_API, LiveClock_API
from nautilus_trader.core.rust.core cimport CVec, UUID4_t

cdef extern from "../includes/backtest.h":

    # Provides a means of accumulating and draining time event handlers.
    cdef struct TimeEventAccumulator:
        pass

    cdef struct TimeEventAccumulatorAPI:
        TimeEventAccumulator *_0;

    TimeEventAccumulatorAPI time_event_accumulator_new();

    void time_event_accumulator_drop(TimeEventAccumulatorAPI accumulator);

    void time_event_accumulator_advance_clock(TimeEventAccumulatorAPI *accumulator,
                                              TestClock_API *clock,
                                              uint64_t to_time_ns,
                                              uint8_t set_time);

    CVec time_event_accumulator_drain(TimeEventAccumulatorAPI *accumulator);
