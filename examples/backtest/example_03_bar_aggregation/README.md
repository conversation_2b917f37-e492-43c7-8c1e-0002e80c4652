# Bar aggregation example

This example demonstrates how to use NautilusTrader's **bar aggregation** features to create higher timeframe bars
from lower timeframe data.

The strategy shows how to:

- Load 1-minute bar data from a CSV file.
- Create 5-minute bars from 1-minute data using internal aggregation.
- Handle both timeframes independently in the same strategy.

This helps you understand how bar aggregation works in NautilusTrader and how to handle multiple timeframes
in your strategies.
