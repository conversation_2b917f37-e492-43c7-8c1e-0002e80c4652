This is a simple example of how to use NautilusTrader's **Timer** feature in a strategy.

The strategy works by running action at regular time intervals while also handling market data events.
It shows how **timer events** and **market data** can work separately at the same time.

**What this strategy does:**

- Uses **NautilusTrader’s timer** to trigger events on schedule.
- Handles **market data** and **timer events** independently.

This helps you see how timers work alongside market data processing without interfering with each other. 🚀
