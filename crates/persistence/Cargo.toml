[package]
name = "nautilus-persistence"
readme = "README.md"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
authors.workspace = true
license.workspace = true
description.workspace = true
categories.workspace = true
keywords.workspace = true
documentation.workspace = true
repository.workspace = true
homepage.workspace = true

[lib]
name = "nautilus_persistence"
crate-type = ["rlib", "staticlib", "cdylib"]

[features]
default = []
extension-module = [
  "pyo3/extension-module",
  "nautilus-core/extension-module",
  "nautilus-model/extension-module",
  "nautilus-serialization/extension-module",
]
ffi = [
  "nautilus-core/ffi",
  "nautilus-model/ffi",
]
python = [
  "pyo3",
  "nautilus-core/ffi",
  "nautilus-core/python",
  "nautilus-model/python",
  "nautilus-serialization/python",
]
high-precision = [
  "nautilus-serialization/high-precision",
  "nautilus-model/high-precision",
]

[package.metadata.docs.rs]
all-features = true
rustdoc-args = ["--cfg", "docsrs"]

[dependencies]
nautilus-core = { workspace = true, features = ["ffi"] }
nautilus-common = { workspace = true }
nautilus-model = { workspace = true, features = ["stubs"] }
nautilus-serialization = { workspace = true, features = ["python"] }

futures = { workspace = true }
heck = { workspace = true }
itertools = { workspace = true }
log = { workspace = true }
pyo3 = { workspace = true, optional = true }
serde_json = { workspace = true }
serde = { workspace = true }
tokio = { workspace = true }
anyhow = { workspace = true }
arrow = { workspace = true }
parquet = { workspace = true }
binary-heap-plus = "0.5.0"
compare = "0.1.0"
datafusion = { version = "47.0.0", default-features = false, features = [
  "parquet",
  "regex_expressions",
  "unicode_expressions",
] }
object_store = { version = "0.12.0", features = ["aws"] }
unbounded-interval-tree = "1.1.2"
regex = "1.10.3"
glob = "0.3.1"
url = "2.5.0"

[dev-dependencies]
nautilus-testkit = { workspace = true }
criterion = { workspace = true }
pretty_assertions = { workspace = true }
rand = { workspace = true }
rstest = { workspace = true }
tempfile = { workspace = true }
quickcheck = "1"
quickcheck_macros = "1"
[target.'cfg(target_os = "linux")'.dependencies]
procfs = "0.17.0"

[[bench]]
name = "persistence"
path = "benches/persistence.rs"
harness = false

[[bin]]
name = "to_json"
path = "bin/to_json.rs"
required-features = ["python"]

[[bin]]
name = "to_parquet"
path = "bin/to_parquet.rs"
required-features = ["python"]
