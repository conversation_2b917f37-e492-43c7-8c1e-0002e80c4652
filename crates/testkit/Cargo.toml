[package]
name = "nautilus-testkit"
readme = "README.md"
version.workspace = true
edition.workspace = true
rust-version.workspace = true
authors.workspace = true
license.workspace = true
description.workspace = true
categories.workspace = true
keywords.workspace = true
documentation.workspace = true
repository.workspace = true
homepage.workspace = true

[lib]
name = "nautilus_testkit"
crate-type = ["rlib", "staticlib"]

[features]
default = []
extension-module = [
  "pyo3/extension-module",
  "nautilus-core/extension-module",
  "nautilus-common/extension-module",
  "nautilus-model/extension-module",
]
python = ["pyo3", "nautilus-core/python", "nautilus-common/python", "nautilus-model/python"]
high-precision = ["nautilus-model/high-precision"]

[dependencies]
nautilus-core = { workspace = true }
nautilus-common = { workspace = true }
nautilus-model = { workspace = true, features = ["stubs"] }

anyhow = { workspace = true }
hex = { workspace = true }
pyo3 = { workspace = true, optional = true }
tokio = { workspace = true }
reqwest = { workspace = true }
ring = { workspace = true }
serde_json = { workspace = true }

[dev-dependencies]
axum = { workspace = true }
rstest = { workspace = true }
tempfile = { workspace = true }
