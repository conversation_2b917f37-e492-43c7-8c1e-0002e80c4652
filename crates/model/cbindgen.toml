language = "C"
include_version = true
autogen_warning = "/* Warning, this file is autogenerated by cbindgen. Don't modify this manually. */"
includes = []
sys_includes = ["stdint.h", "Python.h"]
no_includes = true
tab_width = 4
after_includes = "\n#ifdef __SIZEOF_INT128__\n    typedef __uint128_t uint128_t;\n    typedef __int128_t int128_t;\n#endif"

[defines]
"feature = high-precision" = "HIGH_PRECISION"

[enum]
rename_variants = "ScreamingSnakeCase"

[export]
exclude = [
    "BarAggregation",
    "OrderId",
    "OrderSideSpecified",
]

[export.rename]
"u128" = "uint128_t"
"i128" = "int128_t"
"i32" = "int32_t"
"bool" = "uint8_t"
"Ustr" = "char*"
"AccountId" = "AccountId_t"
"Bar" = "Bar_t"
"BarAggregation" = "uint8_t"
"BarSpecification" = "BarSpecification_t"
"BarType" = "BarType_t"
"BookOrder" = "BookOrder_t"
"OwnBookOrder" = "OwnBookOrder_t"
"OrderSideSpecified" = "OrderSide"
"ClientId" = "ClientId_t"
"ClientOrderId" = "ClientOrderId_t"
"ComponentId" = "ComponentId_t"
"Currency" = "Currency_t"
"Data" = "Data_t"
"ExecAlgorithmId" = "ExecAlgorithmId_t"
"InstrumentId" = "InstrumentId_t"
"Money" = "Money_t"
"NonZeroUsize" = "uintptr_t"
"OrderId" = "uint64_t"
"OrderBookDelta" = "OrderBookDelta_t"
"OrderBookDeltas" = "OrderBookDeltas_t"
"OrderBookDepth10" = "OrderBookDepth10_t"
"OrderInitialized" = "OrderInitialized_t"
"OrderDenied" = "OrderDenied_t"
"OrderEmulated" = "OrderEmulated_t"
"OrderReleased" = "OrderReleased_t"
"OrderSubmitted" = "OrderSubmitted_t"
"OrderAccepted" = "OrderAccepted_t"
"OrderRejected" = "OrderRejected_t"
"OrderCanceled" = "OrderCanceled_t"
"OrderListId" = "OrderListId_t"
"PositionId" = "PositionId_t"
"Price" = "Price_t"
"Quantity" = "Quantity_t"
"QuoteTick" = "QuoteTick_t"
"StrategyId" = "StrategyId_t"
"Symbol" = "Symbol_t"
"TimedeltaNanos" = "int64_t"
"TradeId" = "TradeId_t"
"TradeTick" = "TradeTick_t"
"TraderId" = "TraderId_t"
"UnixNanos" = "uint64_t"
"UUID4" = "UUID4_t"
"Venue" = "Venue_t"
"VenueOrderId" = "VenueOrderId_t"
"MarkPriceUpdate" = "MarkPriceUpdate_t"
"IndexPriceUpdate" = "IndexPriceUpdate_t"
"InstrumentClose" = "InstrumentClose_t"
